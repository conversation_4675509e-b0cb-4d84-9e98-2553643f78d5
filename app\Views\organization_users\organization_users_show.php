<?= $this->extend('templates/dakoii_admin_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid py-4">
    <!-- Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card card-dark">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="text-primary-custom mb-2">
                                <i class="bi bi-person me-2"></i>View Organization User
                            </h2>
                            <p class="text-light mb-0">Detailed information about <?= esc($user['name']) ?></p>
                        </div>
                        <div>
                            <a href="<?= base_url('dakoii/organization-users') ?>" class="btn btn-secondary">
                                <i class="bi bi-arrow-left me-2"></i>Back to List
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- User Details -->
    <div class="row">
        <!-- Basic Information -->
        <div class="col-md-8">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-person-fill me-2"></i>User Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Full Name</label>
                            <div class="fw-bold"><?= esc($user['name']) ?></div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Email Address</label>
                            <div class="fw-bold">
                                <i class="bi bi-envelope me-2"></i><?= esc($user['email']) ?>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Phone Number</label>
                            <div class="fw-bold">
                                <i class="bi bi-telephone me-2"></i><?= esc($user['phone']) ?>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Position</label>
                            <div class="fw-bold"><?= esc($user['position'] ?? 'Not specified') ?></div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Role</label>
                            <div>
                                <span class="badge <?= $user['role'] == 'user' ? 'bg-success' : 'bg-secondary' ?> fs-6">
                                    <?= ucfirst($user['role']) ?>
                                </span>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label text-muted">Status</label>
                            <div>
                                <?php
                                $statusClass = match($user['status']) {
                                    'active' => 'bg-success',
                                    'inactive' => 'bg-warning',
                                    'suspended' => 'bg-danger',
                                    default => 'bg-secondary'
                                };
                                ?>
                                <span class="badge <?= $statusClass ?> fs-6">
                                    <?= ucfirst($user['status']) ?>
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label text-muted">Organization</label>
                            <div class="d-flex align-items-center">
                                <div class="avatar-md bg-info rounded-circle d-flex align-items-center justify-content-center me-3">
                                    <i class="bi bi-building text-white"></i>
                                </div>
                                <div>
                                    <div class="fw-bold"><?= esc($user['org_name'] ?? 'N/A') ?></div>
                                    <small class="text-muted">Code: <?= esc($user['org_code'] ?? 'N/A') ?></small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-12 mb-3">
                            <label class="form-label text-muted">ID Photo Path</label>
                            <div class="fw-bold"><?= esc($user['id_photo']) ?></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Permissions & Status -->
        <div class="col-md-4">
            <div class="card card-dark">
                <div class="card-header">
                    <h5 class="card-title text-primary-custom mb-0">
                        <i class="bi bi-shield-check me-2"></i>Permissions & Status
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label text-muted">Admin Privileges</label>
                        <div>
                            <?php if ($user['is_admin']): ?>
                                <span class="badge bg-warning fs-6">
                                    <i class="bi bi-shield-check me-1"></i>Admin
                                </span>
                            <?php else: ?>
                                <span class="badge bg-secondary fs-6">
                                    <i class="bi bi-dash me-1"></i>No Admin Access
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Supervisor Privileges</label>
                        <div>
                            <?php if ($user['is_supervisor']): ?>
                                <span class="badge bg-info fs-6">
                                    <i class="bi bi-person-badge me-1"></i>Supervisor
                                </span>
                            <?php else: ?>
                                <span class="badge bg-secondary fs-6">
                                    <i class="bi bi-dash me-1"></i>No Supervisor Access
                                </span>
                            <?php endif; ?>
                        </div>
                    </div>

                    <hr>

                    <div class="mb-3">
                        <label class="form-label text-muted">Created</label>
                        <div class="fw-bold">
                            <i class="bi bi-calendar me-2"></i>
                            <?= date('F j, Y \a\t g:i A', strtotime($user['created_at'])) ?>
                        </div>
                        <?php if ($user['created_by']): ?>
                            <small class="text-muted">by User ID: <?= $user['created_by'] ?></small>
                        <?php endif; ?>
                    </div>

                    <div class="mb-3">
                        <label class="form-label text-muted">Last Updated</label>
                        <div class="fw-bold">
                            <i class="bi bi-clock me-2"></i>
                            <?= date('F j, Y \a\t g:i A', strtotime($user['updated_at'])) ?>
                        </div>
                        <?php if ($user['updated_by']): ?>
                            <small class="text-muted">by User ID: <?= $user['updated_by'] ?></small>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Action Buttons -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="d-flex justify-content-end gap-2">
                <a href="<?= base_url('dakoii/organization-users') ?>" class="btn btn-secondary">
                    <i class="bi bi-list me-2"></i>Back to List
                </a>
                <a href="<?= base_url('dakoii/organization-users/' . $user['id'] . '/edit') ?>" class="btn btn-warning">
                    <i class="bi bi-pencil me-2"></i>Edit User
                </a>
                <form method="post" action="<?= base_url('dakoii/organization-users/' . $user['id'] . '/delete') ?>" 
                      class="d-inline" onsubmit="return confirm('Are you sure you want to delete this user?')">
                    <?= csrf_field() ?>
                    <button type="submit" class="btn btn-danger">
                        <i class="bi bi-trash me-2"></i>Delete User
                    </button>
                </form>
            </div>
        </div>
    </div>
</div>

<style>
.avatar-md {
    width: 48px;
    height: 48px;
    font-size: 20px;
}
</style>
<?= $this->endSection() ?>
