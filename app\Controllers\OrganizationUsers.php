<?php

namespace App\Controllers;

use App\Models\UserModel;
use App\Models\DakoiiOrgModel;
use CodeIgniter\Controller;

class OrganizationUsers extends Controller
{
    protected $userModel;
    protected $dakoiiOrgModel;
    
    public function __construct()
    {
        $this->userModel = new UserModel();
        $this->dakoiiOrgModel = new DakoiiOrgModel();
    }
    
    /**
     * Check if user is authenticated
     */
    private function checkAuth()
    {
        if (!session()->get('dakoii_logged_in')) {
            return redirect()->to('dakoii')->with('error', 'Please login to access this page.');
        }
        return null;
    }
    
    /**
     * Display list of organization users
     */
    public function index()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $users = $this->userModel->getUsersWithOrg();
        
        $data = [
            'title' => 'Organization Users Management',
            'users' => $users
        ];
        
        return view('organization_users/organization_users_index', $data);
    }
    
    /**
     * Show create form
     */
    public function new()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $organizations = $this->dakoiiOrgModel->getActiveOrgs();
        
        $data = [
            'title' => 'Add New Organization User',
            'user' => [],
            'organizations' => $organizations
        ];
        
        return view('organization_users/organization_users_create', $data);
    }
    
    /**
     * Handle create form submission
     */
    public function create()
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $rules = [
            'org_id' => 'required|integer',
            'name' => 'required|max_length[255]',
            'email' => 'required|valid_email|max_length[500]|is_unique[users.email]',
            'password' => 'required|min_length[6]',
            'confirm_password' => 'required|matches[password]',
            'phone' => 'required|max_length[200]',
            'role' => 'required|in_list[user,guest]',
            'is_admin' => 'permit_empty|in_list[0,1]',
            'is_supervisor' => 'permit_empty|in_list[0,1]',
            'position' => 'permit_empty|max_length[255]',
            'id_photo' => 'required|max_length[500]',
            'status' => 'required|in_list[active,inactive,suspended]'
        ];
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'org_id' => $this->request->getPost('org_id'),
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'password' => $this->request->getPost('password'),
            'phone' => $this->request->getPost('phone'),
            'role' => $this->request->getPost('role'),
            'is_admin' => $this->request->getPost('is_admin') ?? 0,
            'is_supervisor' => $this->request->getPost('is_supervisor') ?? 0,
            'position' => $this->request->getPost('position'),
            'id_photo' => $this->request->getPost('id_photo'),
            'status' => $this->request->getPost('status'),
            'created_by' => session()->get('dakoii_user_id')
        ];
        
        if ($this->userModel->insert($data)) {
            return redirect()->to('dakoii/organization-users')->with('success', 'Organization user created successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create organization user.');
        }
    }
    
    /**
     * Show single organization user
     */
    public function show($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $user = $this->userModel->select('users.*, dakoii_org.org_name, dakoii_org.org_code')
                               ->join('dakoii_org', 'dakoii_org.id = users.org_id', 'left')
                               ->find($id);
        
        if (!$user) {
            return redirect()->to('dakoii/organization-users')->with('error', 'Organization user not found.');
        }
        
        $data = [
            'title' => 'View Organization User',
            'user' => $user
        ];
        
        return view('organization_users/organization_users_show', $data);
    }
    
    /**
     * Show edit form
     */
    public function edit($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $user = $this->userModel->find($id);
        
        if (!$user) {
            return redirect()->to('dakoii/organization-users')->with('error', 'Organization user not found.');
        }
        
        $organizations = $this->dakoiiOrgModel->getActiveOrgs();
        
        $data = [
            'title' => 'Edit Organization User',
            'user' => $user,
            'organizations' => $organizations
        ];
        
        return view('organization_users/organization_users_edit', $data);
    }
    
    /**
     * Handle edit form submission
     */
    public function update($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $user = $this->userModel->find($id);
        
        if (!$user) {
            return redirect()->to('dakoii/organization-users')->with('error', 'Organization user not found.');
        }
        
        $rules = [
            'org_id' => 'required|integer',
            'name' => 'required|max_length[255]',
            'email' => "required|valid_email|max_length[500]|is_unique[users.email,id,{$id}]",
            'phone' => 'required|max_length[200]',
            'role' => 'required|in_list[user,guest]',
            'is_admin' => 'permit_empty|in_list[0,1]',
            'is_supervisor' => 'permit_empty|in_list[0,1]',
            'position' => 'permit_empty|max_length[255]',
            'id_photo' => 'required|max_length[500]',
            'status' => 'required|in_list[active,inactive,suspended]'
        ];
        
        // Only validate password if provided
        if ($this->request->getPost('password')) {
            $rules['password'] = 'min_length[6]';
            $rules['confirm_password'] = 'matches[password]';
        }
        
        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }
        
        $data = [
            'org_id' => $this->request->getPost('org_id'),
            'name' => $this->request->getPost('name'),
            'email' => $this->request->getPost('email'),
            'phone' => $this->request->getPost('phone'),
            'role' => $this->request->getPost('role'),
            'is_admin' => $this->request->getPost('is_admin') ?? 0,
            'is_supervisor' => $this->request->getPost('is_supervisor') ?? 0,
            'position' => $this->request->getPost('position'),
            'id_photo' => $this->request->getPost('id_photo'),
            'status' => $this->request->getPost('status'),
            'updated_by' => session()->get('dakoii_user_id')
        ];
        
        // Only update password if provided
        if ($this->request->getPost('password')) {
            $data['password'] = $this->request->getPost('password');
        }
        
        if ($this->userModel->update($id, $data)) {
            return redirect()->to('dakoii/organization-users')->with('success', 'Organization user updated successfully.');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update organization user.');
        }
    }
    
    /**
     * Handle delete request
     */
    public function delete($id)
    {
        $authCheck = $this->checkAuth();
        if ($authCheck) return $authCheck;
        
        $user = $this->userModel->find($id);
        
        if (!$user) {
            return redirect()->to('dakoii/organization-users')->with('error', 'Organization user not found.');
        }
        
        // Soft delete with deleted_by tracking
        $deleteData = [
            'deleted_by' => session()->get('dakoii_user_id')
        ];
        
        if ($this->userModel->update($id, $deleteData) && $this->userModel->delete($id)) {
            return redirect()->to('dakoii/organization-users')->with('success', 'Organization user deleted successfully.');
        } else {
            return redirect()->to('dakoii/organization-users')->with('error', 'Failed to delete organization user.');
        }
    }
}
